"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/beneficiaries/new/page",{

/***/ "(app-pages-browser)/./app/beneficiaries/new/page.tsx":
/*!****************************************!*\
  !*** ./app/beneficiaries/new/page.tsx ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ NewBeneficiaryPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _components_layout_dashboard_layout__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/layout/dashboard-layout */ \"(app-pages-browser)/./components/layout/dashboard-layout.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_forms_multi_step_form__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/forms/multi-step-form */ \"(app-pages-browser)/./components/forms/multi-step-form.tsx\");\n/* harmony import */ var _components_forms_steps_personal_details_step__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/forms/steps/personal-details-step */ \"(app-pages-browser)/./components/forms/steps/personal-details-step.tsx\");\n/* harmony import */ var _components_forms_steps_contact_information_step__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/forms/steps/contact-information-step */ \"(app-pages-browser)/./components/forms/steps/contact-information-step.tsx\");\n/* harmony import */ var _components_forms_steps_eligibility_criteria_step__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/forms/steps/eligibility-criteria-step */ \"(app-pages-browser)/./components/forms/steps/eligibility-criteria-step.tsx\");\n/* harmony import */ var _components_forms_steps_documentation_upload_step__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/forms/steps/documentation-upload-step */ \"(app-pages-browser)/./components/forms/steps/documentation-upload-step.tsx\");\n/* harmony import */ var _components_forms_steps_review_submit_step__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/forms/steps/review-submit-step */ \"(app-pages-browser)/./components/forms/steps/review-submit-step.tsx\");\n/* harmony import */ var _lib_validation_beneficiary_registration__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/lib/validation/beneficiary-registration */ \"(app-pages-browser)/./lib/validation/beneficiary-registration.ts\");\n/* harmony import */ var _lib_utils_form_validation__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/lib/utils/form-validation */ \"(app-pages-browser)/./lib/utils/form-validation.ts\");\n/* harmony import */ var _lib_utils_accessibility__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/lib/utils/accessibility */ \"(app-pages-browser)/./lib/utils/accessibility.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction NewBeneficiaryPage() {\n    _s();\n    const { data: session } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)() || {};\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_lib_validation_beneficiary_registration__WEBPACK_IMPORTED_MODULE_14__.defaultFormData);\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [stepValidation, setStepValidation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Load draft on component mount - moved here to avoid hooks rule violation\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect(()=>{\n        const savedDraft = localStorage.getItem(\"beneficiary-draft\");\n        if (savedDraft) {\n            try {\n                const draftData = JSON.parse(savedDraft);\n                setFormData(draftData);\n                sonner__WEBPACK_IMPORTED_MODULE_5__.toast.info(t(\"draft_loaded\"));\n            } catch (error) {\n                console.error(\"Error loading draft:\", error);\n                localStorage.removeItem(\"beneficiary-draft\");\n            }\n        }\n    }, [\n        t\n    ]);\n    if (!session?.user) {\n        return null;\n    }\n    // Check if user has access to beneficiary management\n    const hasAccess = [\n        \"reception_staff\",\n        \"researcher\",\n        \"department_head\",\n        \"admin_manager\",\n        \"minister\",\n        \"system_admin\"\n    ].includes(session.user.role);\n    if (!hasAccess) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_dashboard_layout__WEBPACK_IMPORTED_MODULE_6__.DashboardLayout, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-64\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold mb-2\",\n                            children: t(\"access_denied\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\beneficiaries\\\\new\\\\page.tsx\",\n                            lineNumber: 57,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-muted-foreground\",\n                            children: t(\"no_registration_access\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\beneficiaries\\\\new\\\\page.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\beneficiaries\\\\new\\\\page.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\beneficiaries\\\\new\\\\page.tsx\",\n                lineNumber: 55,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\beneficiaries\\\\new\\\\page.tsx\",\n            lineNumber: 54,\n            columnNumber: 7\n        }, this);\n    }\n    // Memoize validation change handlers to prevent infinite loops\n    const handlePersonalDetailsValidation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((isValid)=>{\n        setStepValidation((prev)=>({\n                ...prev,\n                0: isValid\n            }));\n    }, []);\n    const handleContactInfoValidation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((isValid)=>{\n        setStepValidation((prev)=>({\n                ...prev,\n                1: isValid\n            }));\n    }, []);\n    const handleEligibilityValidation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((isValid)=>{\n        setStepValidation((prev)=>({\n                ...prev,\n                2: isValid\n            }));\n    }, []);\n    const handleDocumentationValidation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((isValid)=>{\n        setStepValidation((prev)=>({\n                ...prev,\n                3: isValid\n            }));\n    }, []);\n    const handleReviewValidation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((isValid)=>{\n        setStepValidation((prev)=>({\n                ...prev,\n                4: isValid\n            }));\n    }, []);\n    // Define the form steps with memoized callbacks\n    const steps = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>[\n            {\n                id: \"personal-details\",\n                title: t(\"personal_details_step\"),\n                description: t(\"personal_details_description\"),\n                component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_forms_steps_personal_details_step__WEBPACK_IMPORTED_MODULE_9__.PersonalDetailsStep, {\n                    onValidationChange: handlePersonalDetailsValidation\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\beneficiaries\\\\new\\\\page.tsx\",\n                    lineNumber: 93,\n                    columnNumber: 9\n                }, this),\n                isValid: stepValidation[0]\n            },\n            {\n                id: \"contact-information\",\n                title: t(\"contact_information_step\"),\n                description: t(\"contact_information_description\"),\n                component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_forms_steps_contact_information_step__WEBPACK_IMPORTED_MODULE_10__.ContactInformationStep, {\n                    onValidationChange: handleContactInfoValidation\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\beneficiaries\\\\new\\\\page.tsx\",\n                    lineNumber: 104,\n                    columnNumber: 9\n                }, this),\n                isValid: stepValidation[1]\n            },\n            {\n                id: \"eligibility-criteria\",\n                title: t(\"eligibility_criteria_step\"),\n                description: t(\"select_applicable_categories\"),\n                component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_forms_steps_eligibility_criteria_step__WEBPACK_IMPORTED_MODULE_11__.EligibilityCriteriaStep, {\n                    onValidationChange: handleEligibilityValidation\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\beneficiaries\\\\new\\\\page.tsx\",\n                    lineNumber: 115,\n                    columnNumber: 9\n                }, this),\n                isValid: stepValidation[2]\n            },\n            {\n                id: \"documentation-upload\",\n                title: t(\"documentation_upload_step\"),\n                description: t(\"upload_documents_description\"),\n                component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_forms_steps_documentation_upload_step__WEBPACK_IMPORTED_MODULE_12__.DocumentationUploadStep, {\n                    onValidationChange: handleDocumentationValidation\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\beneficiaries\\\\new\\\\page.tsx\",\n                    lineNumber: 126,\n                    columnNumber: 9\n                }, this),\n                isValid: stepValidation[3]\n            },\n            {\n                id: \"review-submit\",\n                title: t(\"review_submit_step\"),\n                description: t(\"review_information_description\"),\n                component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_forms_steps_review_submit_step__WEBPACK_IMPORTED_MODULE_13__.ReviewSubmitStep, {\n                    onValidationChange: handleReviewValidation\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\beneficiaries\\\\new\\\\page.tsx\",\n                    lineNumber: 137,\n                    columnNumber: 9\n                }, this),\n                isValid: stepValidation[4]\n            }\n        ], [\n        t,\n        stepValidation,\n        handlePersonalDetailsValidation,\n        handleContactInfoValidation,\n        handleEligibilityValidation,\n        handleDocumentationValidation,\n        handleReviewValidation\n    ]);\n    const handleStepChange = (stepIndex)=>{\n        // Announce step change to screen readers\n        _lib_utils_accessibility__WEBPACK_IMPORTED_MODULE_16__.ScreenReader.announceStepChange(stepIndex, steps.length, steps[stepIndex].title, t);\n    };\n    const handleDataChange = (data)=>{\n        setFormData(data);\n    };\n    const handleSaveDraft = async ()=>{\n        try {\n            // In a real app, this would save to backend\n            localStorage.setItem(\"beneficiary-draft\", JSON.stringify(formData));\n            sonner__WEBPACK_IMPORTED_MODULE_5__.toast.success(t(\"draft_saved_successfully\"));\n        } catch (error) {\n            console.error(\"Error saving draft:\", error);\n            sonner__WEBPACK_IMPORTED_MODULE_5__.toast.error(t(\"error_saving_draft\"));\n        }\n    };\n    const handleSubmit = async ()=>{\n        setIsSubmitting(true);\n        try {\n            // Check for duplicates before submitting\n            if (formData.personalDetails) {\n                const duplicateCheck = await (0,_lib_utils_form_validation__WEBPACK_IMPORTED_MODULE_15__.checkForDuplicates)({\n                    nationalId: formData.personalDetails.nationalId,\n                    fullNameAr: formData.personalDetails.fullNameAr,\n                    fullNameEn: formData.personalDetails.fullNameEn,\n                    phoneNumber: formData.contactInformation?.phoneNumber,\n                    email: formData.contactInformation?.email\n                });\n                if (duplicateCheck.isDuplicate) {\n                    sonner__WEBPACK_IMPORTED_MODULE_5__.toast.error(t(\"duplicate_beneficiary_found\"), {\n                        description: t(\"duplicate_check_failed\")\n                    });\n                    setIsSubmitting(false);\n                    return;\n                }\n            }\n            // Simulate API submission\n            await new Promise((resolve)=>setTimeout(resolve, 2000));\n            // In a real app, this would submit to backend\n            console.log(\"Submitting beneficiary registration:\", formData);\n            // Clear draft\n            localStorage.removeItem(\"beneficiary-draft\");\n            // Show success message\n            sonner__WEBPACK_IMPORTED_MODULE_5__.toast.success(t(\"beneficiary_registered_successfully\"), {\n                description: t(\"beneficiary_registration_success_description\")\n            });\n            // Announce success to screen readers\n            _lib_utils_accessibility__WEBPACK_IMPORTED_MODULE_16__.ScreenReader.announceProgress(100, t);\n            // Redirect to beneficiaries list\n            router.push(\"/beneficiaries\");\n        } catch (error) {\n            console.error(\"Error submitting form:\", error);\n            sonner__WEBPACK_IMPORTED_MODULE_5__.toast.error(t(\"error_submitting_form\"));\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    const handleGoBack = ()=>{\n        router.push(\"/beneficiaries\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_dashboard_layout__WEBPACK_IMPORTED_MODULE_6__.DashboardLayout, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                            variant: \"outline\",\n                            onClick: handleGoBack,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\beneficiaries\\\\new\\\\page.tsx\",\n                                    lineNumber: 236,\n                                    columnNumber: 13\n                                }, this),\n                                t(\"back_to_beneficiaries\")\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\beneficiaries\\\\new\\\\page.tsx\",\n                            lineNumber: 235,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold\",\n                                    children: t(\"register_new_beneficiary\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\beneficiaries\\\\new\\\\page.tsx\",\n                                    lineNumber: 240,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-muted-foreground\",\n                                    children: t(\"beneficiary_registration_description\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\beneficiaries\\\\new\\\\page.tsx\",\n                                    lineNumber: 241,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\beneficiaries\\\\new\\\\page.tsx\",\n                            lineNumber: 239,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\beneficiaries\\\\new\\\\page.tsx\",\n                    lineNumber: 234,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_forms_multi_step_form__WEBPACK_IMPORTED_MODULE_8__.MultiStepForm, {\n                    steps: steps,\n                    initialData: formData,\n                    onStepChange: handleStepChange,\n                    onDataChange: handleDataChange,\n                    onSaveDraft: handleSaveDraft,\n                    onSubmit: handleSubmit,\n                    showSaveDraft: true\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\beneficiaries\\\\new\\\\page.tsx\",\n                    lineNumber: 248,\n                    columnNumber: 9\n                }, this),\n                isSubmitting && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"fixed inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-card p-6 rounded-lg shadow-lg text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\beneficiaries\\\\new\\\\page.tsx\",\n                                lineNumber: 262,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"font-medium\",\n                                children: t(\"submitting_registration\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\beneficiaries\\\\new\\\\page.tsx\",\n                                lineNumber: 263,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-muted-foreground\",\n                                children: t(\"please_wait\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\beneficiaries\\\\new\\\\page.tsx\",\n                                lineNumber: 264,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\beneficiaries\\\\new\\\\page.tsx\",\n                        lineNumber: 261,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\beneficiaries\\\\new\\\\page.tsx\",\n                    lineNumber: 260,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\beneficiaries\\\\new\\\\page.tsx\",\n            lineNumber: 232,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\beneficiaries\\\\new\\\\page.tsx\",\n        lineNumber: 231,\n        columnNumber: 5\n    }, this);\n}\n_s(NewBeneficiaryPage, \"828HTvjmzl1ERXRYm5Car8wvias=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession,\n        react_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation,\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter\n    ];\n});\n_c = NewBeneficiaryPage;\nvar _c;\n$RefreshReg$(_c, \"NewBeneficiaryPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/beneficiaries/new/page.tsx\n"));

/***/ })

});