"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/beneficiaries/new/page",{

/***/ "(app-pages-browser)/./components/forms/steps/documentation-upload-step.tsx":
/*!**************************************************************!*\
  !*** ./components/forms/steps/documentation-upload-step.tsx ***!
  \**************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DocumentationUploadStep: function() { return /* binding */ DocumentationUploadStep; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(app-pages-browser)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_File_FileCheck_FileText_Image_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,File,FileCheck,FileText,Image,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-check.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_File_FileCheck_FileText_Image_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,File,FileCheck,FileText,Image,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_File_FileCheck_FileText_Image_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,File,FileCheck,FileText,Image,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_File_FileCheck_FileText_Image_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,File,FileCheck,FileText,Image,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_File_FileCheck_FileText_Image_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,File,FileCheck,FileText,Image,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_File_FileCheck_FileText_Image_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,File,FileCheck,FileText,Image,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_File_FileCheck_FileText_Image_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,File,FileCheck,FileText,Image,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_progress__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/progress */ \"(app-pages-browser)/./components/ui/progress.tsx\");\n/* harmony import */ var _components_ui_form__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/form */ \"(app-pages-browser)/./components/ui/form.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./components/ui/alert.tsx\");\n/* harmony import */ var _lib_validation_beneficiary_registration__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/lib/validation/beneficiary-registration */ \"(app-pages-browser)/./lib/validation/beneficiary-registration.ts\");\n/* harmony import */ var _components_forms_multi_step_form__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/forms/multi-step-form */ \"(app-pages-browser)/./components/forms/multi-step-form.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ DocumentationUploadStep auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst documentTypes = [\n    {\n        value: \"national_id\",\n        labelKey: \"national_id_document\",\n        required: true,\n        icon: _barrel_optimize_names_AlertCircle_File_FileCheck_FileText_Image_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"]\n    },\n    {\n        value: \"income_certificate\",\n        labelKey: \"income_certificate\",\n        required: false,\n        icon: _barrel_optimize_names_AlertCircle_File_FileCheck_FileText_Image_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"]\n    },\n    {\n        value: \"family_card\",\n        labelKey: \"family_card\",\n        required: false,\n        icon: _barrel_optimize_names_AlertCircle_File_FileCheck_FileText_Image_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"]\n    },\n    {\n        value: \"medical_report\",\n        labelKey: \"medical_report\",\n        required: false,\n        icon: _barrel_optimize_names_AlertCircle_File_FileCheck_FileText_Image_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"]\n    },\n    {\n        value: \"other\",\n        labelKey: \"other_document\",\n        required: false,\n        icon: _barrel_optimize_names_AlertCircle_File_FileCheck_FileText_Image_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"]\n    }\n];\nconst MAX_FILE_SIZE = 5 * 1024 * 1024 // 5MB\n;\nconst ACCEPTED_FILE_TYPES = {\n    \"image/*\": [\n        \".jpeg\",\n        \".jpg\",\n        \".png\",\n        \".gif\"\n    ],\n    \"application/pdf\": [\n        \".pdf\"\n    ],\n    \"application/msword\": [\n        \".doc\"\n    ],\n    \"application/vnd.openxmlformats-officedocument.wordprocessingml.document\": [\n        \".docx\"\n    ]\n};\nconst DocumentationUploadStep = ({ onValidationChange })=>{\n    _s();\n    const { t, i18n } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();\n    const { formData, updateFormData } = (0,_components_forms_multi_step_form__WEBPACK_IMPORTED_MODULE_12__.useMultiStepForm)();\n    const [uploadedDocuments, setUploadedDocuments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(formData.documentation?.documents || []);\n    const form = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_17__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__.zodResolver)(_lib_validation_beneficiary_registration__WEBPACK_IMPORTED_MODULE_11__.documentationSchema),\n        defaultValues: {\n            documents: formData.documentation?.documents || [],\n            nationalIdUploaded: formData.documentation?.nationalIdUploaded || false,\n            additionalNotes: formData.documentation?.additionalNotes || \"\"\n        },\n        mode: \"onChange\"\n    });\n    const { watch, formState: { isValid, errors } } = form;\n    // Watch for form changes and update parent form data\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect(()=>{\n        const subscription = watch((value)=>{\n            updateFormData({\n                documentation: value\n            });\n            onValidationChange?.(isValid);\n        });\n        return ()=>subscription.unsubscribe();\n    }, [\n        watch,\n        updateFormData,\n        isValid\n    ]);\n    // Update form when documents change\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect(()=>{\n        const hasNationalId = uploadedDocuments.some((doc)=>doc.type === \"national_id\");\n        form.setValue(\"documents\", uploadedDocuments);\n        form.setValue(\"nationalIdUploaded\", hasNationalId);\n        onValidationChange?.(isValid && uploadedDocuments.length > 0 && hasNationalId);\n    }, [\n        uploadedDocuments,\n        form,\n        isValid\n    ]);\n    const handleFileSelect = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((files)=>{\n        if (!files) return;\n        Array.from(files).forEach((file)=>{\n            // Validate file size\n            if (file.size > MAX_FILE_SIZE) {\n                console.error(\"File too large:\", file.name);\n                return;\n            }\n            // Validate file type\n            const isValidType = Object.keys(ACCEPTED_FILE_TYPES).some((type)=>file.type.match(type) || Object.values(ACCEPTED_FILE_TYPES).flat().some((ext)=>file.name.toLowerCase().endsWith(ext)));\n            if (!isValidType) {\n                console.error(\"Invalid file type:\", file.name);\n                return;\n            }\n            const newDocument = {\n                id: `doc-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,\n                type: \"other\",\n                name: file.name,\n                file,\n                size: file.size,\n                mimeType: file.type,\n                uploadProgress: 0,\n                isUploading: true\n            };\n            setUploadedDocuments((prev)=>[\n                    ...prev,\n                    newDocument\n                ]);\n            // Simulate upload progress\n            simulateUpload(newDocument.id);\n        });\n    }, []);\n    const simulateUpload = (documentId)=>{\n        let progress = 0;\n        const interval = setInterval(()=>{\n            progress += Math.random() * 30;\n            if (progress >= 100) {\n                progress = 100;\n                clearInterval(interval);\n                setUploadedDocuments((prev)=>prev.map((doc)=>doc.id === documentId ? {\n                            ...doc,\n                            uploadProgress: 100,\n                            isUploading: false\n                        } : doc));\n            } else {\n                setUploadedDocuments((prev)=>prev.map((doc)=>doc.id === documentId ? {\n                            ...doc,\n                            uploadProgress: progress\n                        } : doc));\n            }\n        }, 200);\n    };\n    const handleDragOver = (e)=>{\n        e.preventDefault();\n    };\n    const handleDrop = (e)=>{\n        e.preventDefault();\n        const files = e.dataTransfer.files;\n        handleFileSelect(files);\n    };\n    const removeDocument = (documentId)=>{\n        setUploadedDocuments((prev)=>prev.filter((doc)=>doc.id !== documentId));\n    };\n    const updateDocumentType = (documentId, type)=>{\n        setUploadedDocuments((prev)=>prev.map((doc)=>doc.id === documentId ? {\n                    ...doc,\n                    type\n                } : doc));\n    };\n    const formatFileSize = (bytes)=>{\n        if (bytes === 0) return \"0 Bytes\";\n        const k = 1024;\n        const sizes = [\n            \"Bytes\",\n            \"KB\",\n            \"MB\",\n            \"GB\"\n        ];\n        const i = Math.floor(Math.log(bytes) / Math.log(k));\n        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + \" \" + sizes[i];\n    };\n    const getFileIcon = (mimeType)=>{\n        if (mimeType.startsWith(\"image/\")) return _barrel_optimize_names_AlertCircle_File_FileCheck_FileText_Image_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"];\n        if (mimeType === \"application/pdf\") return _barrel_optimize_names_AlertCircle_File_FileCheck_FileText_Image_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"];\n        return _barrel_optimize_names_AlertCircle_File_FileCheck_FileText_Image_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"];\n    };\n    const isRTL = i18n.language === \"ar\";\n    const hasNationalId = uploadedDocuments.some((doc)=>doc.type === \"national_id\");\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_10__.Alert, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_File_FileCheck_FileText_Image_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\documentation-upload-step.tsx\",\n                        lineNumber: 215,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_10__.AlertTitle, {\n                        children: t(\"document_requirements\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\documentation-upload-step.tsx\",\n                        lineNumber: 216,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_10__.AlertDescription, {\n                        children: t(\"document_requirements_description\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\documentation-upload-step.tsx\",\n                        lineNumber: 217,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\documentation-upload-step.tsx\",\n                lineNumber: 214,\n                columnNumber: 7\n            }, undefined),\n            !hasNationalId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_10__.Alert, {\n                variant: \"destructive\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_File_FileCheck_FileText_Image_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\documentation-upload-step.tsx\",\n                        lineNumber: 225,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_10__.AlertTitle, {\n                        children: t(\"required_document_missing\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\documentation-upload-step.tsx\",\n                        lineNumber: 226,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_10__.AlertDescription, {\n                        children: t(\"national_id_document_required\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\documentation-upload-step.tsx\",\n                        lineNumber: 227,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\documentation-upload-step.tsx\",\n                lineNumber: 224,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_File_FileCheck_FileText_Image_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\documentation-upload-step.tsx\",\n                                        lineNumber: 237,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    t(\"upload_documents\")\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\documentation-upload-step.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                children: t(\"upload_documents_description\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\documentation-upload-step.tsx\",\n                                lineNumber: 240,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\documentation-upload-step.tsx\",\n                        lineNumber: 235,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            onDragOver: handleDragOver,\n                            onDrop: handleDrop,\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_13__.cn)(\"border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors\", \"border-muted-foreground/25 hover:border-primary/50\", \"focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\"),\n                            onClick: ()=>document.getElementById(\"file-upload\")?.click(),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    id: \"file-upload\",\n                                    type: \"file\",\n                                    multiple: true,\n                                    accept: \".pdf,.doc,.docx,.jpg,.jpeg,.png,.gif\",\n                                    onChange: (e)=>handleFileSelect(e.target.files),\n                                    className: \"hidden\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\documentation-upload-step.tsx\",\n                                    lineNumber: 255,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_File_FileCheck_FileText_Image_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                    className: \"mx-auto h-12 w-12 text-muted-foreground mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\documentation-upload-step.tsx\",\n                                    lineNumber: 263,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-lg font-medium\",\n                                            children: t(\"drag_drop_files\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\documentation-upload-step.tsx\",\n                                            lineNumber: 265,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-muted-foreground\",\n                                            children: t(\"or_click_to_browse\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\documentation-upload-step.tsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: [\n                                                t(\"supported_formats\"),\n                                                \": PDF, DOC, DOCX, JPG, PNG (Max 5MB)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\documentation-upload-step.tsx\",\n                                            lineNumber: 271,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\documentation-upload-step.tsx\",\n                                    lineNumber: 264,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\documentation-upload-step.tsx\",\n                            lineNumber: 245,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\documentation-upload-step.tsx\",\n                        lineNumber: 244,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\documentation-upload-step.tsx\",\n                lineNumber: 234,\n                columnNumber: 7\n            }, undefined),\n            uploadedDocuments.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_File_FileCheck_FileText_Image_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\documentation-upload-step.tsx\",\n                                    lineNumber: 284,\n                                    columnNumber: 15\n                                }, undefined),\n                                t(\"uploaded_documents\"),\n                                \" (\",\n                                uploadedDocuments.length,\n                                \")\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\documentation-upload-step.tsx\",\n                            lineNumber: 283,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\documentation-upload-step.tsx\",\n                        lineNumber: 282,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: uploadedDocuments.map((document1)=>{\n                                const FileIcon = getFileIcon(document1.mimeType);\n                                const isRequired = document1.type === \"national_id\";\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-4 p-4 border rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FileIcon, {\n                                            className: \"h-8 w-8 text-muted-foreground flex-shrink-0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\documentation-upload-step.tsx\",\n                                            lineNumber: 299,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 min-w-0\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2 mb-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-medium truncate\",\n                                                            children: document1.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\documentation-upload-step.tsx\",\n                                                            lineNumber: 303,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        isRequired && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                            variant: \"destructive\",\n                                                            className: \"text-xs\",\n                                                            children: t(\"required\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\documentation-upload-step.tsx\",\n                                                            lineNumber: 305,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\documentation-upload-step.tsx\",\n                                                    lineNumber: 302,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: formatFileSize(document1.size)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\documentation-upload-step.tsx\",\n                                                    lineNumber: 311,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                document1.isUploading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_8__.Progress, {\n                                                            value: document1.uploadProgress,\n                                                            className: \"h-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\documentation-upload-step.tsx\",\n                                                            lineNumber: 317,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-muted-foreground mt-1\",\n                                                            children: [\n                                                                t(\"uploading\"),\n                                                                \" \",\n                                                                Math.round(document1.uploadProgress || 0),\n                                                                \"%\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\documentation-upload-step.tsx\",\n                                                            lineNumber: 318,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\documentation-upload-step.tsx\",\n                                                    lineNumber: 316,\n                                                    columnNumber: 25\n                                                }, undefined),\n                                                !document1.isUploading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: document1.type,\n                                                        onChange: (e)=>updateDocumentType(document1.id, e.target.value),\n                                                        className: \"text-sm border rounded px-2 py-1\",\n                                                        children: documentTypes.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: type.value,\n                                                                children: t(type.labelKey)\n                                                            }, type.value, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\documentation-upload-step.tsx\",\n                                                                lineNumber: 332,\n                                                                columnNumber: 31\n                                                            }, undefined))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\documentation-upload-step.tsx\",\n                                                        lineNumber: 326,\n                                                        columnNumber: 27\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\documentation-upload-step.tsx\",\n                                                    lineNumber: 325,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\documentation-upload-step.tsx\",\n                                            lineNumber: 301,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            variant: \"ghost\",\n                                            size: \"sm\",\n                                            onClick: ()=>removeDocument(document1.id),\n                                            className: \"flex-shrink-0\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_File_FileCheck_FileText_Image_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\documentation-upload-step.tsx\",\n                                                lineNumber: 347,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\documentation-upload-step.tsx\",\n                                            lineNumber: 341,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, document1.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\documentation-upload-step.tsx\",\n                                    lineNumber: 295,\n                                    columnNumber: 19\n                                }, undefined);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\documentation-upload-step.tsx\",\n                            lineNumber: 289,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\documentation-upload-step.tsx\",\n                        lineNumber: 288,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\documentation-upload-step.tsx\",\n                lineNumber: 281,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                children: t(\"additional_notes\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\documentation-upload-step.tsx\",\n                                lineNumber: 360,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                children: t(\"additional_notes_description\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\documentation-upload-step.tsx\",\n                                lineNumber: 361,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\documentation-upload-step.tsx\",\n                        lineNumber: 359,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_9__.Form, {\n                            ...form,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_9__.FormField, {\n                                control: form.control,\n                                name: \"additionalNotes\",\n                                render: ({ field })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_9__.FormItem, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_9__.FormLabel, {\n                                                children: [\n                                                    t(\"notes\"),\n                                                    \" (\",\n                                                    t(\"optional\"),\n                                                    \")\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\documentation-upload-step.tsx\",\n                                                lineNumber: 372,\n                                                columnNumber: 19\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_9__.FormControl, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__.Textarea, {\n                                                    placeholder: t(\"enter_additional_notes\"),\n                                                    ...field,\n                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_13__.cn)(\"min-h-20\", errors.additionalNotes && \"border-destructive\", isRTL && \"text-right\"),\n                                                    dir: isRTL ? \"rtl\" : \"ltr\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\documentation-upload-step.tsx\",\n                                                    lineNumber: 374,\n                                                    columnNumber: 21\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\documentation-upload-step.tsx\",\n                                                lineNumber: 373,\n                                                columnNumber: 19\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_9__.FormDescription, {\n                                                children: t(\"additional_notes_help\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\documentation-upload-step.tsx\",\n                                                lineNumber: 385,\n                                                columnNumber: 19\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_9__.FormMessage, {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\documentation-upload-step.tsx\",\n                                                lineNumber: 388,\n                                                columnNumber: 19\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\documentation-upload-step.tsx\",\n                                        lineNumber: 371,\n                                        columnNumber: 17\n                                    }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\documentation-upload-step.tsx\",\n                                lineNumber: 367,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\documentation-upload-step.tsx\",\n                            lineNumber: 366,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\documentation-upload-step.tsx\",\n                        lineNumber: 365,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\documentation-upload-step.tsx\",\n                lineNumber: 358,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\documentation-upload-step.tsx\",\n        lineNumber: 212,\n        columnNumber: 5\n    }, undefined);\n};\n_s(DocumentationUploadStep, \"oQ86+TkfsXpPy/gtq9lukE3BviM=\", false, function() {\n    return [\n        react_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation,\n        _components_forms_multi_step_form__WEBPACK_IMPORTED_MODULE_12__.useMultiStepForm,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_17__.useForm\n    ];\n});\n_c = DocumentationUploadStep;\nvar _c;\n$RefreshReg$(_c, \"DocumentationUploadStep\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/forms/steps/documentation-upload-step.tsx\n"));

/***/ })

});