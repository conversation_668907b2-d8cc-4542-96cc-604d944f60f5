"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/beneficiaries/new/page",{

/***/ "(app-pages-browser)/./components/forms/steps/review-submit-step.tsx":
/*!*******************************************************!*\
  !*** ./components/forms/steps/review-submit-step.tsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ReviewSubmitStep: function() { return /* binding */ ReviewSubmitStep; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(app-pages-browser)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Edit_FileText_Heart_Phone_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Edit,FileText,Heart,Phone,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Edit_FileText_Heart_Phone_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Edit,FileText,Heart,Phone,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Edit_FileText_Heart_Phone_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Edit,FileText,Heart,Phone,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Edit_FileText_Heart_Phone_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Edit,FileText,Heart,Phone,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Edit_FileText_Heart_Phone_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Edit,FileText,Heart,Phone,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Edit_FileText_Heart_Phone_User_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Edit,FileText,Heart,Phone,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=format!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/format.mjs\");\n/* harmony import */ var date_fns_locale__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! date-fns/locale */ \"(app-pages-browser)/./node_modules/date-fns/locale/ar.mjs\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_checkbox__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/checkbox */ \"(app-pages-browser)/./components/ui/checkbox.tsx\");\n/* harmony import */ var _components_ui_form__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/form */ \"(app-pages-browser)/./components/ui/form.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./components/ui/alert.tsx\");\n/* harmony import */ var _lib_validation_beneficiary_registration__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/validation/beneficiary-registration */ \"(app-pages-browser)/./lib/validation/beneficiary-registration.ts\");\n/* harmony import */ var _components_forms_multi_step_form__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/forms/multi-step-form */ \"(app-pages-browser)/./components/forms/multi-step-form.tsx\");\n/* harmony import */ var _lib_mock_data__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/lib/mock-data */ \"(app-pages-browser)/./lib/mock-data.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ ReviewSubmitStep auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst ReviewSubmitStep = ({ onValidationChange })=>{\n    _s();\n    const { t, i18n } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();\n    const { formData, updateFormData, goToStep } = (0,_components_forms_multi_step_form__WEBPACK_IMPORTED_MODULE_11__.useMultiStepForm)();\n    const form = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_14__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__.zodResolver)(_lib_validation_beneficiary_registration__WEBPACK_IMPORTED_MODULE_10__.reviewSubmitSchema),\n        defaultValues: {\n            termsAccepted: formData.reviewSubmit?.termsAccepted || false,\n            dataAccuracyConfirmed: formData.reviewSubmit?.dataAccuracyConfirmed || false,\n            privacyPolicyAccepted: formData.reviewSubmit?.privacyPolicyAccepted || false\n        },\n        mode: \"onChange\"\n    });\n    const { watch, formState: { isValid, errors } } = form;\n    // Watch for form changes and update parent form data\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect(()=>{\n        const subscription = watch((value)=>{\n            updateFormData({\n                reviewSubmit: value\n            });\n            onValidationChange?.(isValid);\n        });\n        return ()=>subscription.unsubscribe();\n    }, [\n        watch,\n        updateFormData,\n        isValid\n    ]);\n    // Notify parent of initial validation state\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect(()=>{\n        onValidationChange?.(isValid);\n    }, [\n        isValid\n    ]);\n    const isRTL = i18n.language === \"ar\";\n    // Helper function to format currency\n    const formatCurrency = (amount)=>{\n        return new Intl.NumberFormat(isRTL ? \"ar-SA\" : \"en-US\", {\n            style: \"currency\",\n            currency: \"SAR\",\n            maximumFractionDigits: 0\n        }).format(amount);\n    };\n    const personalDetails = formData.personalDetails;\n    const contactInfo = formData.contactInformation;\n    const eligibility = formData.eligibilityCriteria;\n    const documentation = formData.documentation;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_9__.Alert, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Edit_FileText_Heart_Phone_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_9__.AlertTitle, {\n                        children: t(\"review_information\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_9__.AlertDescription, {\n                        children: t(\"review_information_description\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                lineNumber: 93,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Edit_FileText_Heart_Phone_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                            lineNumber: 106,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        t(\"personal_information\")\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                    lineNumber: 105,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    onClick: ()=>goToStep(0),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Edit_FileText_Heart_Phone_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        t(\"edit\")\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                            lineNumber: 104,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid gap-4 md:grid-cols-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-muted-foreground\",\n                                            children: t(\"name_arabic\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                            lineNumber: 118,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-right\",\n                                            dir: \"rtl\",\n                                            children: personalDetails?.fullNameAr\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                            lineNumber: 119,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-muted-foreground\",\n                                            children: t(\"name_english\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            dir: \"ltr\",\n                                            children: personalDetails?.fullNameEn\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                            lineNumber: 123,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-muted-foreground\",\n                                            children: t(\"national_id\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                            lineNumber: 126,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"font-mono\",\n                                            dir: \"ltr\",\n                                            children: personalDetails?.nationalId\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-muted-foreground\",\n                                            children: t(\"date_of_birth\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                            lineNumber: 130,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: personalDetails?.dateOfBirth && (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_18__.format)(personalDetails.dateOfBirth, \"PPP\", {\n                                                locale: isRTL ? date_fns_locale__WEBPACK_IMPORTED_MODULE_19__.ar : undefined\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                            lineNumber: 131,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                    lineNumber: 129,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-muted-foreground\",\n                                            children: t(\"gender\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                            lineNumber: 138,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: personalDetails?.gender && t(personalDetails.gender)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                            lineNumber: 139,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                    lineNumber: 137,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-muted-foreground\",\n                                            children: t(\"marital_status\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: personalDetails?.maritalStatus && t(personalDetails.maritalStatus)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                            lineNumber: 143,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                            lineNumber: 116,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                lineNumber: 102,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Edit_FileText_Heart_Phone_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                            lineNumber: 154,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        t(\"contact_info\")\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    onClick: ()=>goToStep(1),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Edit_FileText_Heart_Phone_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                            lineNumber: 158,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        t(\"edit\")\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                    lineNumber: 157,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                            lineNumber: 152,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                        lineNumber: 151,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid gap-4 md:grid-cols-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-muted-foreground\",\n                                            children: t(\"phone_number\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                            lineNumber: 166,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"font-mono\",\n                                            dir: \"ltr\",\n                                            children: contactInfo?.phoneNumber\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                            lineNumber: 167,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-muted-foreground\",\n                                            children: t(\"email\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                            lineNumber: 170,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            dir: \"ltr\",\n                                            children: contactInfo?.email || t(\"not_provided\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"md:col-span-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-muted-foreground\",\n                                            children: t(\"address\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                            lineNumber: 174,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_13__.cn)(isRTL && \"text-right\"),\n                                            dir: isRTL ? \"rtl\" : \"ltr\",\n                                            children: contactInfo?.address\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                            lineNumber: 175,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-muted-foreground\",\n                                            children: t(\"city\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                            lineNumber: 180,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_13__.cn)(isRTL && \"text-right\"),\n                                            dir: isRTL ? \"rtl\" : \"ltr\",\n                                            children: contactInfo?.city\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                    lineNumber: 179,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-muted-foreground\",\n                                            children: t(\"region\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                            lineNumber: 186,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_13__.cn)(isRTL && \"text-right\"),\n                                            dir: isRTL ? \"rtl\" : \"ltr\",\n                                            children: contactInfo?.region\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                    lineNumber: 185,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                            lineNumber: 164,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                lineNumber: 150,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Edit_FileText_Heart_Phone_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                            lineNumber: 200,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        t(\"eligibility_criteria\")\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                    lineNumber: 199,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    onClick: ()=>goToStep(2),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Edit_FileText_Heart_Phone_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                            lineNumber: 204,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        t(\"edit\")\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                    lineNumber: 203,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                            lineNumber: 198,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                        lineNumber: 197,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-muted-foreground\",\n                                            children: t(\"primary_category\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                            lineNumber: 212,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                            variant: \"outline\",\n                                            className: \"mt-1\",\n                                            children: eligibility?.primaryCategory && (isRTL ? _lib_mock_data__WEBPACK_IMPORTED_MODULE_12__.zakatCategoryLabels[eligibility.primaryCategory]?.ar : _lib_mock_data__WEBPACK_IMPORTED_MODULE_12__.zakatCategoryLabels[eligibility.primaryCategory]?.en)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                            lineNumber: 213,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                    lineNumber: 211,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-muted-foreground\",\n                                            children: t(\"applicable_categories\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-2 mt-1\",\n                                            children: eligibility?.zakatCategories?.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                    variant: \"secondary\",\n                                                    children: isRTL ? _lib_mock_data__WEBPACK_IMPORTED_MODULE_12__.zakatCategoryLabels[category]?.ar : _lib_mock_data__WEBPACK_IMPORTED_MODULE_12__.zakatCategoryLabels[category]?.en\n                                                }, category, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                                    lineNumber: 226,\n                                                    columnNumber: 19\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                            lineNumber: 224,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                    lineNumber: 222,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid gap-4 md:grid-cols-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-muted-foreground\",\n                                                    children: t(\"family_size\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                                    lineNumber: 234,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: eligibility?.familySize\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                                    lineNumber: 235,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                            lineNumber: 233,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-muted-foreground\",\n                                                    children: t(\"dependents\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                                    lineNumber: 238,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: eligibility?.dependents\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                                    lineNumber: 239,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                            lineNumber: 237,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-muted-foreground\",\n                                                    children: t(\"monthly_income\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                                    lineNumber: 242,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: eligibility?.monthlyIncome ? formatCurrency(eligibility.monthlyIncome) : t(\"not_provided\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                                    lineNumber: 243,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                            lineNumber: 241,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                    lineNumber: 232,\n                                    columnNumber: 13\n                                }, undefined),\n                                eligibility?.hasSpecialNeeds && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-muted-foreground\",\n                                            children: t(\"special_needs_details\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                            lineNumber: 248,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_13__.cn)(\"text-sm\", isRTL && \"text-right\"),\n                                            dir: isRTL ? \"rtl\" : \"ltr\",\n                                            children: eligibility.specialNeedsDescription\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                            lineNumber: 249,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                    lineNumber: 247,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                            lineNumber: 210,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                        lineNumber: 209,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                lineNumber: 196,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Edit_FileText_Heart_Phone_User_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                            lineNumber: 263,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        t(\"uploaded_documents\")\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                    lineNumber: 262,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    onClick: ()=>goToStep(3),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Edit_FileText_Heart_Phone_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                            lineNumber: 267,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        t(\"edit\")\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                    lineNumber: 266,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                            lineNumber: 261,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                        lineNumber: 260,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: [\n                                documentation?.documents?.map((doc, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3 p-3 border rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Edit_FileText_Heart_Phone_User_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                className: \"h-5 w-5 text-muted-foreground\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium\",\n                                                        children: doc.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                                        lineNumber: 278,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-muted-foreground\",\n                                                        children: [\n                                                            t(doc.type),\n                                                            \" • \",\n                                                            Math.round(doc.size / 1024),\n                                                            \" KB\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                                        lineNumber: 279,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                                lineNumber: 277,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                variant: doc.type === \"national_id\" ? \"destructive\" : \"secondary\",\n                                                children: doc.type === \"national_id\" ? t(\"required\") : t(\"optional\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                                lineNumber: 283,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                        lineNumber: 275,\n                                        columnNumber: 15\n                                    }, undefined)),\n                                (!documentation?.documents || documentation.documents.length === 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-muted-foreground text-center py-4\",\n                                    children: t(\"no_documents_uploaded\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                    lineNumber: 289,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                            lineNumber: 273,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                        lineNumber: 272,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                lineNumber: 259,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                children: t(\"terms_and_conditions\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                lineNumber: 300,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                children: t(\"terms_conditions_description\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                lineNumber: 301,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                        lineNumber: 299,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_8__.Form, {\n                            ...form,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_8__.FormField, {\n                                        control: form.control,\n                                        name: \"termsAccepted\",\n                                        render: ({ field })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_8__.FormItem, {\n                                                className: \"flex flex-row items-start space-x-3 space-y-0\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_8__.FormControl, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_7__.Checkbox, {\n                                                            checked: field.value,\n                                                            onCheckedChange: field.onChange\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                                            lineNumber: 314,\n                                                            columnNumber: 23\n                                                        }, void 0)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                                        lineNumber: 313,\n                                                        columnNumber: 21\n                                                    }, void 0),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-1 leading-none\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_8__.FormLabel, {\n                                                                children: t(\"accept_terms_conditions\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                                                lineNumber: 320,\n                                                                columnNumber: 23\n                                                            }, void 0),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_8__.FormDescription, {\n                                                                children: t(\"terms_conditions_text\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                                                lineNumber: 323,\n                                                                columnNumber: 23\n                                                            }, void 0),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_8__.FormMessage, {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                                                lineNumber: 326,\n                                                                columnNumber: 23\n                                                            }, void 0)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                                        lineNumber: 319,\n                                                        columnNumber: 21\n                                                    }, void 0)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                                lineNumber: 312,\n                                                columnNumber: 19\n                                            }, void 0)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                        lineNumber: 308,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_8__.FormField, {\n                                        control: form.control,\n                                        name: \"dataAccuracyConfirmed\",\n                                        render: ({ field })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_8__.FormItem, {\n                                                className: \"flex flex-row items-start space-x-3 space-y-0\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_8__.FormControl, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_7__.Checkbox, {\n                                                            checked: field.value,\n                                                            onCheckedChange: field.onChange\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                                            lineNumber: 338,\n                                                            columnNumber: 23\n                                                        }, void 0)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                                        lineNumber: 337,\n                                                        columnNumber: 21\n                                                    }, void 0),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-1 leading-none\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_8__.FormLabel, {\n                                                                children: t(\"confirm_data_accuracy\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                                                lineNumber: 344,\n                                                                columnNumber: 23\n                                                            }, void 0),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_8__.FormDescription, {\n                                                                children: t(\"data_accuracy_text\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                                                lineNumber: 347,\n                                                                columnNumber: 23\n                                                            }, void 0),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_8__.FormMessage, {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                                                lineNumber: 350,\n                                                                columnNumber: 23\n                                                            }, void 0)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                                        lineNumber: 343,\n                                                        columnNumber: 21\n                                                    }, void 0)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                                lineNumber: 336,\n                                                columnNumber: 19\n                                            }, void 0)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                        lineNumber: 332,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_8__.FormField, {\n                                        control: form.control,\n                                        name: \"privacyPolicyAccepted\",\n                                        render: ({ field })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_8__.FormItem, {\n                                                className: \"flex flex-row items-start space-x-3 space-y-0\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_8__.FormControl, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_7__.Checkbox, {\n                                                            checked: field.value,\n                                                            onCheckedChange: field.onChange\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                                            lineNumber: 362,\n                                                            columnNumber: 23\n                                                        }, void 0)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                                        lineNumber: 361,\n                                                        columnNumber: 21\n                                                    }, void 0),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-1 leading-none\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_8__.FormLabel, {\n                                                                children: t(\"accept_privacy_policy\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                                                lineNumber: 368,\n                                                                columnNumber: 23\n                                                            }, void 0),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_8__.FormDescription, {\n                                                                children: t(\"privacy_policy_text\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                                                lineNumber: 371,\n                                                                columnNumber: 23\n                                                            }, void 0),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_8__.FormMessage, {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                                                lineNumber: 374,\n                                                                columnNumber: 23\n                                                            }, void 0)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                                        lineNumber: 367,\n                                                        columnNumber: 21\n                                                    }, void 0)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                                lineNumber: 360,\n                                                columnNumber: 19\n                                            }, void 0)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                        lineNumber: 356,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                                lineNumber: 307,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                            lineNumber: 306,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                        lineNumber: 305,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n                lineNumber: 298,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\forms\\\\steps\\\\review-submit-step.tsx\",\n        lineNumber: 91,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ReviewSubmitStep, \"UT7P182GNqqncMsrR0yT+hkoH0k=\", false, function() {\n    return [\n        react_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation,\n        _components_forms_multi_step_form__WEBPACK_IMPORTED_MODULE_11__.useMultiStepForm,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_14__.useForm\n    ];\n});\n_c = ReviewSubmitStep;\nvar _c;\n$RefreshReg$(_c, \"ReviewSubmitStep\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/forms/steps/review-submit-step.tsx\n"));

/***/ })

});