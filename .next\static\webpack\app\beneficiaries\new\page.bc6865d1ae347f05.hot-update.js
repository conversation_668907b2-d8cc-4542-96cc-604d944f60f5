"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/beneficiaries/new/page",{

/***/ "(app-pages-browser)/./app/beneficiaries/new/page.tsx":
/*!****************************************!*\
  !*** ./app/beneficiaries/new/page.tsx ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ NewBeneficiaryPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _components_layout_dashboard_layout__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/layout/dashboard-layout */ \"(app-pages-browser)/./components/layout/dashboard-layout.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_forms_multi_step_form__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/forms/multi-step-form */ \"(app-pages-browser)/./components/forms/multi-step-form.tsx\");\n/* harmony import */ var _components_forms_steps_personal_details_step__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/forms/steps/personal-details-step */ \"(app-pages-browser)/./components/forms/steps/personal-details-step.tsx\");\n/* harmony import */ var _components_forms_steps_contact_information_step__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/forms/steps/contact-information-step */ \"(app-pages-browser)/./components/forms/steps/contact-information-step.tsx\");\n/* harmony import */ var _components_forms_steps_eligibility_criteria_step__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/forms/steps/eligibility-criteria-step */ \"(app-pages-browser)/./components/forms/steps/eligibility-criteria-step.tsx\");\n/* harmony import */ var _components_forms_steps_documentation_upload_step__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/forms/steps/documentation-upload-step */ \"(app-pages-browser)/./components/forms/steps/documentation-upload-step.tsx\");\n/* harmony import */ var _components_forms_steps_review_submit_step__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/forms/steps/review-submit-step */ \"(app-pages-browser)/./components/forms/steps/review-submit-step.tsx\");\n/* harmony import */ var _lib_validation_beneficiary_registration__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/lib/validation/beneficiary-registration */ \"(app-pages-browser)/./lib/validation/beneficiary-registration.ts\");\n/* harmony import */ var _lib_utils_form_validation__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/lib/utils/form-validation */ \"(app-pages-browser)/./lib/utils/form-validation.ts\");\n/* harmony import */ var _lib_utils_accessibility__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/lib/utils/accessibility */ \"(app-pages-browser)/./lib/utils/accessibility.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction NewBeneficiaryPage() {\n    _s();\n    const { data: session } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)() || {};\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_lib_validation_beneficiary_registration__WEBPACK_IMPORTED_MODULE_14__.defaultFormData);\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [stepValidation, setStepValidation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Load draft on component mount - moved here to avoid hooks rule violation\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect(()=>{\n        const savedDraft = localStorage.getItem(\"beneficiary-draft\");\n        if (savedDraft) {\n            try {\n                const draftData = JSON.parse(savedDraft);\n                setFormData(draftData);\n                sonner__WEBPACK_IMPORTED_MODULE_5__.toast.info(t(\"draft_loaded\"));\n            } catch (error) {\n                console.error(\"Error loading draft:\", error);\n                localStorage.removeItem(\"beneficiary-draft\");\n            }\n        }\n    }, [\n        t\n    ]);\n    if (!session?.user) {\n        return null;\n    }\n    // Check if user has access to beneficiary management\n    const hasAccess = [\n        \"reception_staff\",\n        \"researcher\",\n        \"department_head\",\n        \"admin_manager\",\n        \"minister\",\n        \"system_admin\"\n    ].includes(session.user.role);\n    if (!hasAccess) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_dashboard_layout__WEBPACK_IMPORTED_MODULE_6__.DashboardLayout, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-64\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold mb-2\",\n                            children: t(\"access_denied\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\beneficiaries\\\\new\\\\page.tsx\",\n                            lineNumber: 57,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-muted-foreground\",\n                            children: t(\"no_registration_access\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\beneficiaries\\\\new\\\\page.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\beneficiaries\\\\new\\\\page.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\beneficiaries\\\\new\\\\page.tsx\",\n                lineNumber: 55,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\beneficiaries\\\\new\\\\page.tsx\",\n            lineNumber: 54,\n            columnNumber: 7\n        }, this);\n    }\n    // Define the form steps\n    const steps = [\n        {\n            id: \"personal-details\",\n            title: t(\"personal_details_step\"),\n            description: t(\"personal_details_description\"),\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_forms_steps_personal_details_step__WEBPACK_IMPORTED_MODULE_9__.PersonalDetailsStep, {\n                onValidationChange: (isValid)=>setStepValidation((prev)=>({\n                            ...prev,\n                            0: isValid\n                        }))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\beneficiaries\\\\new\\\\page.tsx\",\n                lineNumber: 72,\n                columnNumber: 9\n            }, this),\n            isValid: stepValidation[0]\n        },\n        {\n            id: \"contact-information\",\n            title: t(\"contact_information_step\"),\n            description: t(\"contact_information_description\"),\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_forms_steps_contact_information_step__WEBPACK_IMPORTED_MODULE_10__.ContactInformationStep, {\n                onValidationChange: (isValid)=>setStepValidation((prev)=>({\n                            ...prev,\n                            1: isValid\n                        }))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\beneficiaries\\\\new\\\\page.tsx\",\n                lineNumber: 83,\n                columnNumber: 9\n            }, this),\n            isValid: stepValidation[1]\n        },\n        {\n            id: \"eligibility-criteria\",\n            title: t(\"eligibility_criteria_step\"),\n            description: t(\"select_applicable_categories\"),\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_forms_steps_eligibility_criteria_step__WEBPACK_IMPORTED_MODULE_11__.EligibilityCriteriaStep, {\n                onValidationChange: (isValid)=>setStepValidation((prev)=>({\n                            ...prev,\n                            2: isValid\n                        }))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\beneficiaries\\\\new\\\\page.tsx\",\n                lineNumber: 94,\n                columnNumber: 9\n            }, this),\n            isValid: stepValidation[2]\n        },\n        {\n            id: \"documentation-upload\",\n            title: t(\"documentation_upload_step\"),\n            description: t(\"upload_documents_description\"),\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_forms_steps_documentation_upload_step__WEBPACK_IMPORTED_MODULE_12__.DocumentationUploadStep, {\n                onValidationChange: (isValid)=>setStepValidation((prev)=>({\n                            ...prev,\n                            3: isValid\n                        }))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\beneficiaries\\\\new\\\\page.tsx\",\n                lineNumber: 105,\n                columnNumber: 9\n            }, this),\n            isValid: stepValidation[3]\n        },\n        {\n            id: \"review-submit\",\n            title: t(\"review_submit_step\"),\n            description: t(\"review_information_description\"),\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_forms_steps_review_submit_step__WEBPACK_IMPORTED_MODULE_13__.ReviewSubmitStep, {\n                onValidationChange: (isValid)=>setStepValidation((prev)=>({\n                            ...prev,\n                            4: isValid\n                        }))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\beneficiaries\\\\new\\\\page.tsx\",\n                lineNumber: 116,\n                columnNumber: 9\n            }, this),\n            isValid: stepValidation[4]\n        }\n    ];\n    const handleStepChange = (stepIndex)=>{\n        // Announce step change to screen readers\n        _lib_utils_accessibility__WEBPACK_IMPORTED_MODULE_16__.ScreenReader.announceStepChange(stepIndex, steps.length, steps[stepIndex].title, t);\n    };\n    const handleDataChange = (data)=>{\n        setFormData(data);\n    };\n    const handleSaveDraft = async ()=>{\n        try {\n            // In a real app, this would save to backend\n            localStorage.setItem(\"beneficiary-draft\", JSON.stringify(formData));\n            sonner__WEBPACK_IMPORTED_MODULE_5__.toast.success(t(\"draft_saved_successfully\"));\n        } catch (error) {\n            console.error(\"Error saving draft:\", error);\n            sonner__WEBPACK_IMPORTED_MODULE_5__.toast.error(t(\"error_saving_draft\"));\n        }\n    };\n    const handleSubmit = async ()=>{\n        setIsSubmitting(true);\n        try {\n            // Check for duplicates before submitting\n            if (formData.personalDetails) {\n                const duplicateCheck = await (0,_lib_utils_form_validation__WEBPACK_IMPORTED_MODULE_15__.checkForDuplicates)({\n                    nationalId: formData.personalDetails.nationalId,\n                    fullNameAr: formData.personalDetails.fullNameAr,\n                    fullNameEn: formData.personalDetails.fullNameEn,\n                    phoneNumber: formData.contactInformation?.phoneNumber,\n                    email: formData.contactInformation?.email\n                });\n                if (duplicateCheck.isDuplicate) {\n                    sonner__WEBPACK_IMPORTED_MODULE_5__.toast.error(t(\"duplicate_beneficiary_found\"), {\n                        description: t(\"duplicate_check_failed\")\n                    });\n                    setIsSubmitting(false);\n                    return;\n                }\n            }\n            // Simulate API submission\n            await new Promise((resolve)=>setTimeout(resolve, 2000));\n            // In a real app, this would submit to backend\n            console.log(\"Submitting beneficiary registration:\", formData);\n            // Clear draft\n            localStorage.removeItem(\"beneficiary-draft\");\n            // Show success message\n            sonner__WEBPACK_IMPORTED_MODULE_5__.toast.success(t(\"beneficiary_registered_successfully\"), {\n                description: t(\"beneficiary_registration_success_description\")\n            });\n            // Announce success to screen readers\n            _lib_utils_accessibility__WEBPACK_IMPORTED_MODULE_16__.ScreenReader.announceProgress(100, t);\n            // Redirect to beneficiaries list\n            router.push(\"/beneficiaries\");\n        } catch (error) {\n            console.error(\"Error submitting form:\", error);\n            sonner__WEBPACK_IMPORTED_MODULE_5__.toast.error(t(\"error_submitting_form\"));\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    const handleGoBack = ()=>{\n        router.push(\"/beneficiaries\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_dashboard_layout__WEBPACK_IMPORTED_MODULE_6__.DashboardLayout, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                            variant: \"outline\",\n                            onClick: handleGoBack,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\beneficiaries\\\\new\\\\page.tsx\",\n                                    lineNumber: 207,\n                                    columnNumber: 13\n                                }, this),\n                                t(\"back_to_beneficiaries\")\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\beneficiaries\\\\new\\\\page.tsx\",\n                            lineNumber: 206,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold\",\n                                    children: t(\"register_new_beneficiary\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\beneficiaries\\\\new\\\\page.tsx\",\n                                    lineNumber: 211,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-muted-foreground\",\n                                    children: t(\"beneficiary_registration_description\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\beneficiaries\\\\new\\\\page.tsx\",\n                                    lineNumber: 212,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\beneficiaries\\\\new\\\\page.tsx\",\n                            lineNumber: 210,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\beneficiaries\\\\new\\\\page.tsx\",\n                    lineNumber: 205,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_forms_multi_step_form__WEBPACK_IMPORTED_MODULE_8__.MultiStepForm, {\n                    steps: steps,\n                    initialData: formData,\n                    onStepChange: handleStepChange,\n                    onDataChange: handleDataChange,\n                    onSaveDraft: handleSaveDraft,\n                    onSubmit: handleSubmit,\n                    showSaveDraft: true\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\beneficiaries\\\\new\\\\page.tsx\",\n                    lineNumber: 219,\n                    columnNumber: 9\n                }, this),\n                isSubmitting && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"fixed inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-card p-6 rounded-lg shadow-lg text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\beneficiaries\\\\new\\\\page.tsx\",\n                                lineNumber: 233,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"font-medium\",\n                                children: t(\"submitting_registration\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\beneficiaries\\\\new\\\\page.tsx\",\n                                lineNumber: 234,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-muted-foreground\",\n                                children: t(\"please_wait\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\beneficiaries\\\\new\\\\page.tsx\",\n                                lineNumber: 235,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\beneficiaries\\\\new\\\\page.tsx\",\n                        lineNumber: 232,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\beneficiaries\\\\new\\\\page.tsx\",\n                    lineNumber: 231,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\beneficiaries\\\\new\\\\page.tsx\",\n            lineNumber: 203,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\beneficiaries\\\\new\\\\page.tsx\",\n        lineNumber: 202,\n        columnNumber: 5\n    }, this);\n}\n_s(NewBeneficiaryPage, \"fuRhXrU7X4DIMcROrkMAXTwPsZo=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession,\n        react_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation,\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter\n    ];\n});\n_c = NewBeneficiaryPage;\nvar _c;\n$RefreshReg$(_c, \"NewBeneficiaryPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/beneficiaries/new/page.tsx\n"));

/***/ })

});